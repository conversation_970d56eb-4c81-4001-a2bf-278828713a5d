"""
Test script to verify UI functionality and dashboard separation.
This script tests the login system and dashboard routing.
"""

import streamlit as st
import sys
import os

# Add the current directory to the path
sys.path.append(os.path.dirname(__file__))

from login import USERS, TECHNICIANS

def test_login_credentials():
    """Test that login credentials are properly configured."""
    print("Testing login credentials...")
    
    # Test user credentials
    print("\nUser Credentials:")
    for user_id, user_data in USERS.items():
        print(f"  {user_id}: {user_data['name']} - Password: {user_data['password']}")
    
    # Test technician credentials
    print("\nTechnician Credentials:")
    for tech_id, tech_data in TECHNICIANS.items():
        print(f"  {tech_id}: {tech_data['name']} - Password: {tech_data['password']}")
    
    print("\n✅ Login credentials test completed!")

def test_dashboard_separation():
    """Test that dashboard components are properly separated."""
    print("\nTesting dashboard separation...")
    
    try:
        # Test user dashboard imports
        from src.ui.user_dashboard import (
            user_my_tickets_page,
            user_ticket_status_page,
            user_help_page
        )
        print("✅ User dashboard components imported successfully")
        
        # Test technician dashboard imports
        from src.ui.technician_dashboard import (
            technician_dashboard_page,
            technician_my_tickets_page,
            technician_urgent_tickets_page,
            technician_analytics_page
        )
        print("✅ Technician dashboard components imported successfully")
        
        # Test main app imports
        from app import (
            show_user_dashboard,
            show_technician_dashboard,
            create_user_sidebar,
            create_technician_sidebar
        )
        print("✅ Main app dashboard functions imported successfully")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    print("✅ Dashboard separation test completed!")
    return True

def test_authentication_flow():
    """Test the authentication flow logic."""
    print("\nTesting authentication flow...")
    
    # Simulate session states
    test_cases = [
        {"session": {}, "expected": "login_required"},
        {"session": {"user": {"id": "U001", "name": "User1"}, "role": "user"}, "expected": "user_dashboard"},
        {"session": {"technician": {"id": "T101", "name": "Technician1"}, "role": "technician"}, "expected": "technician_dashboard"},
    ]
    
    for i, case in enumerate(test_cases):
        print(f"  Test case {i+1}: {case['expected']}")
        session = case['session']
        
        if 'user' not in session and 'technician' not in session:
            result = "login_required"
        elif session.get('role') == 'user':
            result = "user_dashboard"
        elif session.get('role') == 'technician':
            result = "technician_dashboard"
        else:
            result = "invalid_role"
        
        if result == case['expected']:
            print(f"    ✅ Passed")
        else:
            print(f"    ❌ Failed: expected {case['expected']}, got {result}")
    
    print("✅ Authentication flow test completed!")

def main():
    """Run all tests."""
    print("🧪 Starting UI Functionality Tests")
    print("=" * 50)
    
    test_login_credentials()
    test_dashboard_separation()
    test_authentication_flow()
    
    print("\n" + "=" * 50)
    print("🎉 All tests completed!")
    print("\nTo test the application manually:")
    print("1. Run: streamlit run app.py")
    print("2. Try logging in with these credentials:")
    print("   User: U001 / Pass@001")
    print("   Technician: T101 / Tech@9382xB")
    print("3. Verify that each role sees different navigation options")
    print("4. Test all dashboard pages for both roles")

if __name__ == "__main__":
    main()

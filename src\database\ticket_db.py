import os
from src.database.snowflake_db import SnowflakeConnection
from config import *
import json

class TicketDB:
    def __init__(self, conn=None):
        if conn is not None:
            self.conn = conn
        else:
            self.conn = SnowflakeConnection(
                sf_account=SF_ACCOUNT,
                sf_user=SF_USER,
                sf_password=SF_PASSWORD,
                sf_warehouse=SF_WAREHOUSE,
                sf_database=SF_DATABASE,
                sf_schema=SF_SCHEMA,
                sf_role=SF_ROLE,
                sf_passcode=SF_PASSCODE
            )

    def insert_ticket(self, ticket_data: dict):
        query = '''
        INSERT INTO TEST_DB.PUBLIC.TICKETS (
            TITLE, DESCRIPTION, TICKETTYPE, TICKETNUMBER, TICKETCATEGORY, ISSUETYPE, SUBISSUETYPE, DUEDATETIME, RESOLUTION, USERID, USEREMAIL, TECHNICIANEMAIL, PHONENUMBER
        ) VALUES (%(TITLE)s, %(DESCRIPTION)s, %(TICKETTYPE)s, %(TICKETNUMBER)s, %(TICKETCATEGORY)s, %(ISSUETYPE)s, %(SUBISSUETYPE)s, %(DUEDATETIME)s, %(RESOLUTION)s, %(USERID)s, %(USEREMAIL)s, %(TECHNICIANEMAIL)s, %(PHONENUMBER)s)
        '''
        self.conn.execute_query(query, ticket_data)

    def update_ticket_assignment(self, ticket_number: str, technician_email: str):
        query = '''
        UPDATE TEST_DB.PUBLIC.TICKETS SET TECHNICIANEMAIL = %s WHERE TICKETNUMBER = %s
        '''
        self.conn.execute_query(query, (technician_email, ticket_number))

    def get_tickets_for_user(self, user_id: str):
        query = '''
        SELECT * FROM TEST_DB.PUBLIC.TICKETS WHERE USERID = %s
        '''
        return self.conn.execute_query(query, (user_id,))

    def get_tickets_for_technician(self, technician_email: str):
        query = '''
        SELECT * FROM TEST_DB.PUBLIC.TICKETS WHERE TECHNICIANEMAIL = %s
        '''
        return self.conn.execute_query(query, (technician_email,))

    def get_technician_by_email(self, email: str):
        query = '''
        SELECT * FROM TEST_DB.PUBLIC.TECHNICIAN_DUMMY_DATA WHERE EMAIL = %s
        '''
        results = self.conn.execute_query(query, (email,))
        return results[0] if results else None

    def update_ticket_status(self, ticket_number: str, status: str):
        query = '''
        UPDATE TEST_DB.PUBLIC.TICKETS SET STATUS = %s WHERE TICKETNUMBER = %s
        '''
        self.conn.execute_query(query, (status, ticket_number))

    def add_work_note(self, ticket_number: str, note: str):
        # Fetch current notes
        query_select = '''
        SELECT WORK_NOTES FROM TEST_DB.PUBLIC.TICKETS WHERE TICKETNUMBER = %s
        '''
        result = self.conn.execute_query(query_select, (ticket_number,))
        notes = []
        if result and result[0].get('WORK_NOTES'):
            try:
                notes = json.loads(result[0]['WORK_NOTES'])
            except Exception:
                notes = []
        from datetime import datetime
        notes.append({'note': note, 'time': datetime.now().isoformat()})
        notes_json = json.dumps(notes)
        query_update = '''
        UPDATE TEST_DB.PUBLIC.TICKETS SET WORK_NOTES = %s WHERE TICKETNUMBER = %s
        '''
        self.conn.execute_query(query_update, (notes_json, ticket_number)) 
"""
Test script to verify Snowflake connection optimization.
This script tests that only a single connection is being used.
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from src.database.snowflake_db import SnowflakeConnection
from src.database.ticket_db import TicketDB
from src.agents.intake_agent import IntakeClassificationAgent
from config import *

def test_connection_reuse():
    """Test that components reuse the same connection."""
    print("🧪 Testing Snowflake Connection Optimization")
    print("=" * 50)
    
    # Create a single connection
    print("1. Creating primary Snowflake connection...")
    primary_conn = SnowflakeConnection(
        sf_account=SF_ACCOUNT,
        sf_user=SF_USER,
        sf_password=SF_PASSWORD,
        sf_warehouse=SF_WAREHOUSE,
        sf_database=SF_DATABASE,
        sf_schema=SF_SCHEMA,
        sf_role=SF_ROLE,
        sf_passcode=SF_PASSCODE
    )
    print(f"   Primary connection ID: {id(primary_conn)}")
    
    # Test TicketDB with existing connection
    print("\n2. Creating TicketDB with existing connection...")
    ticket_db = TicketDB(conn=primary_conn)
    print(f"   TicketDB connection ID: {id(ticket_db.conn)}")
    print(f"   ✅ Same connection: {id(ticket_db.conn) == id(primary_conn)}")
    
    # Test IntakeClassificationAgent with existing connection
    print("\n3. Creating IntakeClassificationAgent with existing connection...")
    try:
        agent = IntakeClassificationAgent(
            sf_account=SF_ACCOUNT,
            sf_user=SF_USER,
            sf_password=SF_PASSWORD,
            sf_warehouse=SF_WAREHOUSE,
            sf_database=SF_DATABASE,
            sf_schema=SF_SCHEMA,
            sf_role=SF_ROLE,
            sf_passcode=SF_PASSCODE,
            data_ref_file='data/reference_data.txt',
            db_connection=primary_conn
        )
        print(f"   Agent connection ID: {id(agent.db_connection)}")
        print(f"   ✅ Same connection: {id(agent.db_connection) == id(primary_conn)}")
    except Exception as e:
        print(f"   ⚠️ Agent creation failed: {e}")
    
    # Test connection without reuse (should create new)
    print("\n4. Creating TicketDB without existing connection...")
    ticket_db_new = TicketDB()
    print(f"   New TicketDB connection ID: {id(ticket_db_new.conn)}")
    print(f"   ✅ Different connection: {id(ticket_db_new.conn) != id(primary_conn)}")
    
    print("\n" + "=" * 50)
    print("🎉 Connection optimization test completed!")
    
    return {
        'primary_conn_id': id(primary_conn),
        'ticket_db_conn_id': id(ticket_db.conn),
        'agent_conn_id': id(agent.db_connection) if 'agent' in locals() else None,
        'new_ticket_db_conn_id': id(ticket_db_new.conn),
        'reuse_successful': id(ticket_db.conn) == id(primary_conn)
    }

def test_connection_functionality():
    """Test that the shared connection works properly."""
    print("\n🔧 Testing Connection Functionality")
    print("=" * 50)
    
    try:
        # Create connection
        conn = SnowflakeConnection(
            sf_account=SF_ACCOUNT,
            sf_user=SF_USER,
            sf_password=SF_PASSWORD,
            sf_warehouse=SF_WAREHOUSE,
            sf_database=SF_DATABASE,
            sf_schema=SF_SCHEMA,
            sf_role=SF_ROLE,
            sf_passcode=SF_PASSCODE
        )
        
        # Test basic query
        print("1. Testing basic query...")
        result = conn.execute_query("SELECT CURRENT_TIMESTAMP() as current_time")
        if result:
            print(f"   ✅ Query successful: {result[0]['CURRENT_TIME']}")
        else:
            print("   ❌ Query failed")
        
        # Test TicketDB functionality
        print("\n2. Testing TicketDB functionality...")
        ticket_db = TicketDB(conn=conn)
        
        # Try to query tickets table
        try:
            tickets = ticket_db.conn.execute_query("SELECT COUNT(*) as count FROM TEST_DB.PUBLIC.TICKETS")
            print(f"   ✅ Tickets table accessible: {tickets[0]['COUNT']} tickets found")
        except Exception as e:
            print(f"   ⚠️ Tickets table query failed: {e}")
        
        # Try to query technician table
        try:
            techs = ticket_db.conn.execute_query("SELECT COUNT(*) as count FROM TEST_DB.PUBLIC.TECHNICIAN_DUMMY_DATA")
            print(f"   ✅ Technician table accessible: {techs[0]['COUNT']} technicians found")
        except Exception as e:
            print(f"   ⚠️ Technician table query failed: {e}")
        
        print("\n✅ Connection functionality test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Connection functionality test failed: {e}")
        return False

def main():
    """Run all connection tests."""
    print("🚀 Starting Snowflake Connection Tests")
    print("=" * 60)
    
    # Test connection reuse
    reuse_results = test_connection_reuse()
    
    # Test connection functionality
    functionality_success = test_connection_functionality()
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 60)
    print(f"Connection Reuse: {'✅ PASS' if reuse_results['reuse_successful'] else '❌ FAIL'}")
    print(f"Connection Functionality: {'✅ PASS' if functionality_success else '❌ FAIL'}")
    
    if reuse_results['reuse_successful'] and functionality_success:
        print("\n🎉 All tests passed! Connection optimization is working correctly.")
    else:
        print("\n⚠️ Some tests failed. Please review the connection implementation.")
    
    print("\n💡 Key Benefits of Single Connection:")
    print("   • Reduced memory usage")
    print("   • Faster application startup")
    print("   • Better resource management")
    print("   • Consistent connection state")

if __name__ == "__main__":
    main()

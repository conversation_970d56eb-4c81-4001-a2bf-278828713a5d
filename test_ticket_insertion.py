"""
Test script to verify ticket insertion is working correctly with the updated schema.
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from src.database.snowflake_db import SnowflakeConnection
from src.database.ticket_db import TicketDB
from config import *
from datetime import datetime

def create_sample_ticket_data():
    """Create sample ticket data in the format that comes from the agent."""
    return {
        'title': 'Email Server Connection Issues',
        'description': 'Users are unable to connect to the email server. Getting timeout errors when trying to send emails.',
        'ticket_number': f'TKT-{datetime.now().strftime("%Y%m%d")}-TEST',
        'due_date': '2024-07-20',
        'resolution_note': 'Check email server configuration and restart mail services if necessary.',
        'user_email': '<EMAIL>',
        'phone_number': '555-0123',
        'priority_initial': 'High',
        'date': datetime.now().strftime('%Y-%m-%d'),
        'time': datetime.now().strftime('%H:%M:%S'),
        'user_id': 'U001',
        'classified_data': {
            'TICKETTYPE': {'Value': 'Incident', 'Label': 'Incident'},
            'TICKETCATEGORY': {'Value': 'Email', 'Label': 'Email Systems'},
            'ISSUETYPE': {'Value': 'Email', 'Label': 'Email Issues'},
            'SUBISSUETYPE': {'Value': 'Connection', 'Label': 'Connection Problems'},
            'PRIORITY': {'Value': 'High', 'Label': 'High Priority'}
        },
        'assignment_result': {
            'technician_email': '<EMAIL>',
            'status': 'Assigned',
            'assigned_technician': 'John Smith',
            'technician_id': 'TECH-001'
        }
    }

def test_ticket_insertion():
    """Test inserting a ticket using the updated TicketDB class."""
    print("🧪 Testing Ticket Insertion")
    print("=" * 40)
    
    # Connect to Snowflake
    try:
        conn = SnowflakeConnection(
            sf_account=SF_ACCOUNT,
            sf_user=SF_USER,
            sf_password=SF_PASSWORD,
            sf_warehouse=SF_WAREHOUSE,
            sf_database=SF_DATABASE,
            sf_schema=SF_SCHEMA,
            sf_role=SF_ROLE,
            sf_passcode=SF_PASSCODE
        )
        print("✅ Connected to Snowflake")
    except Exception as e:
        print(f"❌ Failed to connect to Snowflake: {e}")
        return False
    
    # Create TicketDB instance
    ticket_db = TicketDB(conn=conn)
    
    # Create sample ticket data
    ticket_data = create_sample_ticket_data()
    print(f"📝 Created sample ticket: {ticket_data['ticket_number']}")
    
    # Test insertion
    try:
        print("🔄 Inserting ticket into database...")
        ticket_db.insert_ticket(ticket_data)
        print("✅ Ticket inserted successfully!")
    except Exception as e:
        print(f"❌ Failed to insert ticket: {e}")
        return False
    
    # Verify the ticket was inserted
    try:
        print("🔍 Verifying ticket was inserted...")
        query = f"SELECT * FROM TEST_DB.PUBLIC.TICKETS WHERE TICKETNUMBER = '{ticket_data['ticket_number']}'"
        result = conn.execute_query(query)
        
        if result and len(result) > 0:
            ticket = result[0]
            print("✅ Ticket found in database!")
            print(f"   Title: {ticket.get('TITLE', 'N/A')}")
            print(f"   Status: {ticket.get('STATUS', 'N/A')}")
            print(f"   Priority: {ticket.get('PRIORITY', 'N/A')}")
            print(f"   Assigned to: {ticket.get('TECHNICIANEMAIL', 'N/A')}")
            print(f"   Created: {ticket.get('CREATED_DATE', 'N/A')} {ticket.get('CREATED_TIME', 'N/A')}")
        else:
            print("❌ Ticket not found in database")
            return False
            
    except Exception as e:
        print(f"❌ Failed to verify ticket: {e}")
        return False
    
    # Test updating ticket status
    try:
        print("🔄 Testing status update...")
        ticket_db.update_ticket_status(ticket_data['ticket_number'], 'In Progress')
        
        # Verify status update
        query = f"SELECT STATUS FROM TEST_DB.PUBLIC.TICKETS WHERE TICKETNUMBER = '{ticket_data['ticket_number']}'"
        result = conn.execute_query(query)
        
        if result and result[0]['STATUS'] == 'In Progress':
            print("✅ Status updated successfully!")
        else:
            print("❌ Status update failed")
            return False
            
    except Exception as e:
        print(f"❌ Failed to update status: {e}")
        return False
    
    # Test adding work notes
    try:
        print("🔄 Testing work notes...")
        ticket_db.add_work_note(ticket_data['ticket_number'], 'Initial investigation completed. Issue identified.')
        
        # Verify work notes
        query = f"SELECT WORK_NOTES FROM TEST_DB.PUBLIC.TICKETS WHERE TICKETNUMBER = '{ticket_data['ticket_number']}'"
        result = conn.execute_query(query)
        
        if result and result[0]['WORK_NOTES']:
            import json
            notes = json.loads(result[0]['WORK_NOTES'])
            if len(notes) > 0:
                print("✅ Work note added successfully!")
                print(f"   Note: {notes[0]['note']}")
            else:
                print("❌ Work note not found")
                return False
        else:
            print("❌ Work notes update failed")
            return False
            
    except Exception as e:
        print(f"❌ Failed to add work note: {e}")
        return False
    
    # Clean up - delete test ticket
    try:
        print("🧹 Cleaning up test ticket...")
        delete_query = f"DELETE FROM TEST_DB.PUBLIC.TICKETS WHERE TICKETNUMBER = '{ticket_data['ticket_number']}'"
        conn.execute_query(delete_query)
        print("✅ Test ticket cleaned up")
    except Exception as e:
        print(f"⚠️ Failed to clean up test ticket: {e}")
    
    return True

def test_data_mapping():
    """Test the data mapping function separately."""
    print("\n🔧 Testing Data Mapping")
    print("=" * 40)
    
    # Create TicketDB instance (without connection for mapping test)
    ticket_db = TicketDB()
    
    # Create sample ticket data
    ticket_data = create_sample_ticket_data()
    
    try:
        # Test the mapping function
        mapped_data = ticket_db._map_ticket_data_for_db(ticket_data)
        
        print("✅ Data mapping successful!")
        print("📋 Mapped fields:")
        for key, value in mapped_data.items():
            print(f"   {key}: {value}")
        
        # Verify all required fields are present
        required_fields = [
            'TITLE', 'DESCRIPTION', 'TICKETTYPE', 'TICKETNUMBER', 'TICKETCATEGORY',
            'ISSUETYPE', 'SUBISSUETYPE', 'DUEDATETIME', 'RESOLUTION', 'USERID',
            'USEREMAIL', 'TECHNICIANEMAIL', 'PHONENUMBER', 'STATUS', 'PRIORITY',
            'CREATED_DATE', 'CREATED_TIME', 'WORK_NOTES'
        ]
        
        missing_fields = [field for field in required_fields if field not in mapped_data]
        
        if missing_fields:
            print(f"❌ Missing fields: {missing_fields}")
            return False
        else:
            print("✅ All required fields are present in mapped data")
            return True
            
    except Exception as e:
        print(f"❌ Data mapping failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Ticket Insertion Test Suite")
    print("=" * 50)
    
    # Test data mapping first
    mapping_success = test_data_mapping()
    
    # Test actual database insertion
    insertion_success = test_ticket_insertion()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print(f"Data Mapping: {'✅ PASS' if mapping_success else '❌ FAIL'}")
    print(f"Database Insertion: {'✅ PASS' if insertion_success else '❌ FAIL'}")
    
    if mapping_success and insertion_success:
        print("\n🎉 All tests passed! Ticket storage is working correctly.")
        print("✅ Tickets will be properly stored in TEST_DB.PUBLIC.TICKETS")
        print("✅ All required columns are populated")
        print("✅ Status updates and work notes are functional")
    else:
        print("\n⚠️ Some tests failed. Please review the implementation.")
    
    return mapping_success and insertion_success

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)

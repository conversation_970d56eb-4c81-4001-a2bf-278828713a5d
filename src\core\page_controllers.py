"""
Page controllers for TeamLogic-AutoTask.
Handles page routing and navigation logic.
"""

import streamlit as st
from typing import Dict, Any

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))


class PageControllers:
    """Handles page routing and navigation for the application."""
    
    @staticmethod
    def show_user_dashboard(agent, data_manager, ticket_db):
        """Show user dashboard with user-specific navigation and functionality."""
        from ..ui.user_dashboard import (
            user_my_tickets_page,
            user_ticket_status_page,
            user_help_page
        )
        
        # Create user-specific sidebar
        PageControllers.create_user_sidebar(data_manager)
        
        # Show page based on navigation
        current_page = st.session_state.get('page', 'main')
        if current_page == "main":
            PageControllers.main_page(agent, data_manager, ticket_db)
        elif current_page == "user_my_tickets":
            user_my_tickets_page()
        elif current_page == "user_ticket_status":
            user_ticket_status_page()
        elif current_page == "user_help":
            user_help_page()
        else:
            # Default to main page for invalid pages
            st.session_state.page = "main"
            PageControllers.main_page(agent, data_manager, ticket_db)
    
    @staticmethod
    def show_technician_dashboard(agent, data_manager, ticket_db):
        """Show technician dashboard with technician-specific navigation and functionality."""
        from ..ui.technician_dashboard import (
            technician_dashboard_page,
            technician_my_tickets_page,
            technician_urgent_tickets_page,
            technician_analytics_page
        )
        
        # Create technician-specific sidebar
        PageControllers.create_technician_sidebar(data_manager)
        
        # Show page based on navigation
        current_page = st.session_state.get('page', 'technician_dashboard')
        if current_page == "technician_dashboard":
            technician_dashboard_page()
        elif current_page == "technician_my_tickets":
            technician_my_tickets_page()
        elif current_page == "technician_urgent_tickets":
            technician_urgent_tickets_page()
        elif current_page == "technician_analytics":
            technician_analytics_page()
        elif current_page == "technician_all_tickets":
            PageControllers.technician_dashboard_all_tickets_page(ticket_db)
        else:
            # Default to technician dashboard for invalid pages
            st.session_state.page = "technician_dashboard"
            technician_dashboard_page()
    
    @staticmethod
    def create_user_sidebar(data_manager):
        """Create sidebar for regular users."""
        import json
        import os
        
        with st.sidebar:
            # User info and logout
            user_info = st.session_state.get('user', {})
            st.markdown(f"### Welcome, {user_info.get('name', 'User')}!")
            st.caption(f"User ID: {user_info.get('id', 'N/A')}")
            
            if st.button("🚪 Logout", key="user_logout"):
                for key in ['user', 'technician', 'role', 'page']:
                    if key in st.session_state:
                        del st.session_state[key]
                st.rerun()
            
            st.markdown("---")
            st.markdown("## Navigation")
            
            # User Dashboard navigation
            user_pages = {
                "🏠 Home": "main",
                "📋 My Tickets": "user_my_tickets",
                "📊 Ticket Status": "user_ticket_status",
                "❓ Help": "user_help"
            }
            
            current_page = st.session_state.get('page', 'main')
            
            for label, page_key in user_pages.items():
                if st.button(label, key=f"nav_{page_key}", use_container_width=True):
                    st.session_state.page = page_key
                    st.rerun()
            
            st.markdown(f"""
            <div style="margin: 20px 0; padding: 10px; background-color: var(--accent); border-radius: 6px;">
            <small>Current page:</small><br>
            <strong>{current_page.replace('_', ' ').title()}</strong>
            </div>
            """, unsafe_allow_html=True)
            
            st.markdown("---")
            st.markdown("### Quick Stats")
            try:
                from config import KNOWLEDGEBASE_FILE
                if os.path.exists(KNOWLEDGEBASE_FILE):
                    with open(KNOWLEDGEBASE_FILE, 'r') as f:
                        kb_data = json.load(f)
                    total_tickets = len(kb_data)
                else:
                    total_tickets = 0
            except:
                total_tickets = 0
            st.metric("Total Tickets", total_tickets)
            
            st.markdown("---")
            st.markdown("""
            <div style="padding: 10px;">
            <h4>Need Help?</h4>
            <p>Contact IT Support:</p>
            <p>📞 9723100860<br>
            ✉️ <EMAIL></p>
            </div>
            """, unsafe_allow_html=True)
    
    @staticmethod
    def create_technician_sidebar(data_manager):
        """Create sidebar for technicians."""
        import json
        import os
        
        with st.sidebar:
            # Technician info and logout
            tech_info = st.session_state.get('technician', {})
            st.markdown(f"### Welcome, {tech_info.get('name', 'Technician')}!")
            st.caption(f"Tech ID: {tech_info.get('id', 'N/A')}")
            
            if st.button("🚪 Logout", key="tech_logout"):
                for key in ['user', 'technician', 'role', 'page']:
                    if key in st.session_state:
                        del st.session_state[key]
                st.rerun()
            
            st.markdown("---")
            st.markdown("## Navigation")
            
            # Technician Dashboard navigation
            technician_pages = {
                "🔧 Dashboard": "technician_dashboard",
                "📋 My Tickets": "technician_my_tickets",
                "🚨 Urgent Tickets": "technician_urgent_tickets",
                "📊 Analytics": "technician_analytics",
                "📋 All Tickets": "technician_all_tickets"
            }
            
            current_page = st.session_state.get('page', 'technician_dashboard')
            
            for label, page_key in technician_pages.items():
                if st.button(label, key=f"nav_{page_key}", use_container_width=True):
                    st.session_state.page = page_key
                    st.rerun()
            
            st.markdown(f"""
            <div style="margin: 20px 0; padding: 10px; background-color: var(--accent); border-radius: 6px;">
            <small>Current page:</small><br>
            <strong>{current_page.replace('_', ' ').title()}</strong>
            </div>
            """, unsafe_allow_html=True)
            
            st.markdown("---")
            st.markdown("### Quick Actions")
            if st.button("🔄 Refresh Data", key="tech_refresh"):
                st.rerun()
            
            st.markdown("---")
            st.markdown("### Today's Performance")
            # Get technician's tickets for metrics
            try:
                from config import KNOWLEDGEBASE_FILE
                if os.path.exists(KNOWLEDGEBASE_FILE):
                    with open(KNOWLEDGEBASE_FILE, 'r') as f:
                        kb_data = json.load(f)
                    tech_email = f"{tech_info.get('name', '').lower()}@company.com"
                    my_tickets = [entry['new_ticket'] for entry in kb_data
                                if entry['new_ticket'].get('assignment_result', {}).get('technician_email') == tech_email]
                    assigned_count = sum(1 for t in my_tickets if t.get('status', '').lower() in ['assigned', 'open'])
                    completed_count = sum(1 for t in my_tickets if t.get('status', '').lower() in ['resolved', 'closed'])
                    in_progress_count = sum(1 for t in my_tickets if t.get('status', '').lower() == 'in progress')
                    completion_rate = int((completed_count / len(my_tickets)) * 100) if my_tickets else 0
                else:
                    assigned_count = completed_count = in_progress_count = completion_rate = 0
            except:
                assigned_count = completed_count = in_progress_count = completion_rate = 0
                
            st.metric("Assigned", assigned_count)
            st.metric("In Progress", in_progress_count)
            st.metric("Completed", completed_count)
            st.metric("Completion Rate", f"{completion_rate}%")
    
    @staticmethod
    def main_page(agent, data_manager, ticket_db):
        """Main ticket submission page."""
        # This would be imported from the main app or moved to a separate page module
        # For now, this is a placeholder
        st.title("🎫 Submit New Ticket")
        st.write("Main ticket submission functionality would be here.")
    
    @staticmethod
    def technician_dashboard_all_tickets_page(ticket_db):
        """Show all tickets from the database for the technician dashboard."""
        st.title("Technician Dashboard - All Tickets")
        all_tickets = []
        try:
            query = 'SELECT * FROM TEST_DB.PUBLIC.TICKETS'
            all_tickets = ticket_db.conn.execute_query(query)
        except Exception as e:
            st.error(f"Error fetching tickets: {e}")
            return
        
        if not all_tickets:
            st.info("No tickets found in the database.")
            return
        
        st.write(f"Found {len(all_tickets)} tickets in the database:")
        
        # Display tickets in a table
        import pandas as pd
        df = pd.DataFrame(all_tickets)
        st.dataframe(df, use_container_width=True)

"""
Database Schema Check and Update Script
Verifies and updates the TEST_DB.PUBLIC.TICKETS table schema to ensure all required columns exist.
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from src.database.snowflake_db import SnowflakeConnection
from config import *

def get_table_schema(conn, table_name="TEST_DB.PUBLIC.TICKETS"):
    """Get the current schema of the tickets table."""
    try:
        query = f"DESCRIBE TABLE {table_name}"
        result = conn.execute_query(query)
        return result
    except Exception as e:
        print(f"Error getting table schema: {e}")
        return None

def check_required_columns(conn):
    """Check if all required columns exist in the TICKETS table."""
    print("🔍 Checking TICKETS table schema...")
    
    # Required columns for the application
    required_columns = {
        'TITLE': 'VARCHAR(500)',
        'DESCRIPTION': 'TEXT',
        'TICKETTYPE': 'VARCHAR(100)',
        'TICKETNUMBER': 'VARCHAR(50)',
        'TICKETCATEGORY': 'VARCHAR(100)',
        'ISSUETYPE': 'VARCHAR(100)',
        'SUBISSUETYPE': 'VARCHAR(100)',
        'DUEDATETIME': 'VARCHAR(50)',
        'RESOLUTION': 'TEXT',
        'USERID': 'VARCHAR(100)',
        'USEREMAIL': 'VARCHAR(200)',
        'TECHNICIANEMAIL': 'VARCHAR(200)',
        'PHONENUMBER': 'VARCHAR(50)',
        'STATUS': 'VARCHAR(50)',
        'PRIORITY': 'VARCHAR(50)',
        'CREATED_DATE': 'VARCHAR(50)',
        'CREATED_TIME': 'VARCHAR(50)',
        'WORK_NOTES': 'TEXT'
    }
    
    # Get current schema
    current_schema = get_table_schema(conn)
    if not current_schema:
        print("❌ Could not retrieve table schema")
        return False, required_columns
    
    # Extract existing column names
    existing_columns = {row['name'].upper(): row['type'] for row in current_schema}
    
    print(f"📋 Found {len(existing_columns)} existing columns:")
    for col_name, col_type in existing_columns.items():
        print(f"   • {col_name}: {col_type}")
    
    # Check for missing columns
    missing_columns = {}
    for col_name, col_type in required_columns.items():
        if col_name not in existing_columns:
            missing_columns[col_name] = col_type
    
    if missing_columns:
        print(f"\n⚠️ Missing {len(missing_columns)} required columns:")
        for col_name, col_type in missing_columns.items():
            print(f"   • {col_name}: {col_type}")
    else:
        print("\n✅ All required columns exist!")
    
    return len(missing_columns) == 0, missing_columns

def add_missing_columns(conn, missing_columns):
    """Add missing columns to the TICKETS table."""
    if not missing_columns:
        print("✅ No columns to add")
        return True
    
    print(f"\n🔧 Adding {len(missing_columns)} missing columns...")
    
    success_count = 0
    for col_name, col_type in missing_columns.items():
        try:
            # Add default values for certain columns
            default_value = ""
            if col_name == 'STATUS':
                default_value = " DEFAULT 'Open'"
            elif col_name == 'PRIORITY':
                default_value = " DEFAULT 'Medium'"
            elif col_name == 'WORK_NOTES':
                default_value = " DEFAULT '[]'"
            elif col_name in ['CREATED_DATE', 'CREATED_TIME']:
                default_value = " DEFAULT ''"
            
            query = f"ALTER TABLE TEST_DB.PUBLIC.TICKETS ADD COLUMN {col_name} {col_type}{default_value}"
            print(f"   Adding {col_name}...")
            conn.execute_query(query)
            print(f"   ✅ {col_name} added successfully")
            success_count += 1
            
        except Exception as e:
            print(f"   ❌ Failed to add {col_name}: {e}")
    
    print(f"\n📊 Summary: {success_count}/{len(missing_columns)} columns added successfully")
    return success_count == len(missing_columns)

def verify_table_exists(conn):
    """Check if the TICKETS table exists, create if it doesn't."""
    try:
        query = "SELECT COUNT(*) as count FROM TEST_DB.PUBLIC.TICKETS LIMIT 1"
        conn.execute_query(query)
        print("✅ TICKETS table exists")
        return True
    except Exception as e:
        print(f"⚠️ TICKETS table does not exist or is not accessible: {e}")
        return create_tickets_table(conn)

def create_tickets_table(conn):
    """Create the TICKETS table with all required columns."""
    print("🔧 Creating TICKETS table...")
    
    create_table_query = '''
    CREATE TABLE TEST_DB.PUBLIC.TICKETS (
        TITLE VARCHAR(500),
        DESCRIPTION TEXT,
        TICKETTYPE VARCHAR(100) DEFAULT 'Support',
        TICKETNUMBER VARCHAR(50) PRIMARY KEY,
        TICKETCATEGORY VARCHAR(100) DEFAULT 'General',
        ISSUETYPE VARCHAR(100) DEFAULT 'Other',
        SUBISSUETYPE VARCHAR(100) DEFAULT 'General',
        DUEDATETIME VARCHAR(50),
        RESOLUTION TEXT,
        USERID VARCHAR(100),
        USEREMAIL VARCHAR(200),
        TECHNICIANEMAIL VARCHAR(200),
        PHONENUMBER VARCHAR(50),
        STATUS VARCHAR(50) DEFAULT 'Open',
        PRIORITY VARCHAR(50) DEFAULT 'Medium',
        CREATED_DATE VARCHAR(50),
        CREATED_TIME VARCHAR(50),
        WORK_NOTES TEXT DEFAULT '[]'
    )
    '''
    
    try:
        conn.execute_query(create_table_query)
        print("✅ TICKETS table created successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to create TICKETS table: {e}")
        return False

def test_insert_operation(conn):
    """Test inserting a sample ticket to verify the schema works."""
    print("\n🧪 Testing ticket insertion...")
    
    # Sample ticket data
    sample_ticket = {
        'TITLE': 'Test Ticket - Schema Verification',
        'DESCRIPTION': 'This is a test ticket to verify the database schema is working correctly.',
        'TICKETTYPE': 'Support',
        'TICKETNUMBER': f'TEST-{int(datetime.now().timestamp())}',
        'TICKETCATEGORY': 'Testing',
        'ISSUETYPE': 'System Test',
        'SUBISSUETYPE': 'Schema Verification',
        'DUEDATETIME': '2024-12-31',
        'RESOLUTION': 'Test resolution note',
        'USERID': 'TEST_USER',
        'USEREMAIL': '<EMAIL>',
        'TECHNICIANEMAIL': '<EMAIL>',
        'PHONENUMBER': '555-0123',
        'STATUS': 'Open',
        'PRIORITY': 'Low',
        'CREATED_DATE': datetime.now().strftime('%Y-%m-%d'),
        'CREATED_TIME': datetime.now().strftime('%H:%M:%S'),
        'WORK_NOTES': '[]'
    }
    
    try:
        from src.database.ticket_db import TicketDB
        ticket_db = TicketDB(conn=conn)
        
        # Create a mock ticket data in agent format
        agent_ticket_data = {
            'title': sample_ticket['TITLE'],
            'description': sample_ticket['DESCRIPTION'],
            'ticket_number': sample_ticket['TICKETNUMBER'],
            'due_date': sample_ticket['DUEDATETIME'],
            'resolution_note': sample_ticket['RESOLUTION'],
            'user_email': sample_ticket['USEREMAIL'],
            'phone_number': sample_ticket['PHONENUMBER'],
            'priority_initial': sample_ticket['PRIORITY'],
            'classified_data': {
                'TICKETTYPE': {'Value': sample_ticket['TICKETTYPE']},
                'TICKETCATEGORY': {'Value': sample_ticket['TICKETCATEGORY']},
                'ISSUETYPE': {'Value': sample_ticket['ISSUETYPE']},
                'SUBISSUETYPE': {'Value': sample_ticket['SUBISSUETYPE']},
                'PRIORITY': {'Value': sample_ticket['PRIORITY']}
            },
            'assignment_result': {
                'technician_email': sample_ticket['TECHNICIANEMAIL'],
                'status': sample_ticket['STATUS']
            }
        }
        
        # Test the insert operation
        ticket_db.insert_ticket(agent_ticket_data)
        print("✅ Test ticket inserted successfully")
        
        # Clean up - delete the test ticket
        delete_query = f"DELETE FROM TEST_DB.PUBLIC.TICKETS WHERE TICKETNUMBER = '{sample_ticket['TICKETNUMBER']}'"
        conn.execute_query(delete_query)
        print("✅ Test ticket cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ Test insertion failed: {e}")
        return False

def main():
    """Main function to check and update database schema."""
    print("🚀 Database Schema Check and Update")
    print("=" * 50)
    
    # Connect to Snowflake
    try:
        conn = SnowflakeConnection(
            sf_account=SF_ACCOUNT,
            sf_user=SF_USER,
            sf_password=SF_PASSWORD,
            sf_warehouse=SF_WAREHOUSE,
            sf_database=SF_DATABASE,
            sf_schema=SF_SCHEMA,
            sf_role=SF_ROLE,
            sf_passcode=SF_PASSCODE
        )
        print("✅ Connected to Snowflake")
    except Exception as e:
        print(f"❌ Failed to connect to Snowflake: {e}")
        return False
    
    # Check if table exists
    if not verify_table_exists(conn):
        print("❌ Failed to create or access TICKETS table")
        return False
    
    # Check required columns
    all_columns_exist, missing_columns = check_required_columns(conn)
    
    # Add missing columns if needed
    if not all_columns_exist:
        if not add_missing_columns(conn, missing_columns):
            print("❌ Failed to add all required columns")
            return False
    
    # Test the schema with a sample insert
    if not test_insert_operation(conn):
        print("❌ Schema test failed")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 Database schema check completed successfully!")
    print("✅ All required columns are present and working")
    print("✅ Ticket insertion is working correctly")
    
    return True

if __name__ == "__main__":
    from datetime import datetime
    success = main()
    if not success:
        sys.exit(1)
